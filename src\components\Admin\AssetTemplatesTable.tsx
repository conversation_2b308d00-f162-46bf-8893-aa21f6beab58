'use client';

import { useState, useEffect } from 'react';
import {
  Table,
  Text,
  Badge,
  Group,
  ActionIcon,
  Button,
  Modal,
  TextInput,
  Textarea,
  Select,
  Stack,
  Paper,
  Card,
  Grid,
  Alert,
  LoadingOverlay,
  ScrollArea,
  Tooltip
} from '@mantine/core';
import {
  IconEdit,
  IconTrash,
  IconPlus,
  IconEye,
  IconTemplate,
  IconAlertCircle,
  IconInfoCircle
} from '@tabler/icons-react';
import { notifications } from '@mantine/notifications';
import { modals } from '@mantine/modals';
import { fetchData, insertData, updateData, deleteData } from '../../lib/supabase';
import { AssetTemplate, ASSET_TYPES, getAssetTypeColor } from '../../lib/assets-utils';
import AssetDisplay from '../Assets/AssetDisplay';

interface TemplateFormData {
  id: string;
  name: string;
  description: string;
  asset_type: string;
  svg_code: string;
  is_active: boolean;
  profile_email?: string | null;
}

export function AssetTemplatesTable() {
  const [templates, setTemplates] = useState<AssetTemplate[]>([]);
  const [loading, setLoading] = useState(true);
  const [modalOpened, setModalOpened] = useState(false);
  const [previewModalOpened, setPreviewModalOpened] = useState(false);
  const [placeholderInfoModalOpened, setPlaceholderInfoModalOpened] = useState(false);
  const [editingTemplate, setEditingTemplate] = useState<AssetTemplate | null>(null);
  const [selectedTemplate, setSelectedTemplate] = useState<AssetTemplate | null>(null);
  const [formData, setFormData] = useState<TemplateFormData>({
    id: '',
    name: '',
    description: '',
    asset_type: '',
    svg_code: '',
    is_active: true,
    profile_email: null
  });

  useEffect(() => {
    fetchTemplates();
  }, []);

  const fetchTemplates = async () => {
    try {
      setLoading(true);
      const { data, error } = await fetchData('asset_templates', {
        select: '*',
        order: { column: 'created_at', ascending: false }
      });

      if (!error && data) {
        const templatesArray = Array.isArray(data) ? data : [data];
        setTemplates(templatesArray);
      } else if (error) {
        console.error('Error fetching templates:', error);
        notifications.show({
          title: 'Error',
          message: 'Failed to fetch asset templates',
          color: 'red',
        });
      }
    } catch (error) {
      console.error('Error fetching templates:', error);
      notifications.show({
        title: 'Error',
        message: 'Failed to fetch asset templates',
        color: 'red',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCreate = () => {
    setEditingTemplate(null);
    setFormData({
      id: '',
      name: '',
      description: '',
      asset_type: '',
      svg_code: '',
      is_active: true,
      profile_email: null // Admin templates have null profile_email
    });
    setModalOpened(true);
  };

  const handleEdit = (template: AssetTemplate) => {
    setEditingTemplate(template);
    setFormData({
      id: template.id,
      name: template.name,
      description: template.description || '',
      asset_type: template.asset_type,
      svg_code: template.svg_code || '',
      is_active: template.is_active,
      profile_email: template.profile_email
    });
    setModalOpened(true);
  };

  const handlePreview = (template: AssetTemplate) => {
    setSelectedTemplate(template);
    setPreviewModalOpened(true);
  };

  const handleSubmit = async () => {
    try {
      // Validate form data
      if (!formData.id || !formData.name || !formData.asset_type || !formData.svg_code) {
        notifications.show({
          title: 'Validation Error',
          message: 'Please fill in all required fields (ID, Name, Asset Type, and SVG Code)',
          color: 'red',
        });
        return;
      }

      // Basic SVG validation
      if (!formData.svg_code.trim().startsWith('<svg')) {
        notifications.show({
          title: 'Validation Error',
          message: 'SVG Code must start with <svg tag',
          color: 'red',
        });
        return;
      }

      const templatePayload = {
        id: formData.id,
        name: formData.name,
        description: formData.description || null,
        asset_type: formData.asset_type as any,
        svg_code: formData.svg_code,
        is_active: formData.is_active
      };

      if (editingTemplate) {
        // Update existing template
        const { error } = await updateData('asset_templates', templatePayload, {
          column: 'id',
          value: editingTemplate.id
        });

        if (error) {
          throw error;
        }

        notifications.show({
          title: 'Success',
          message: 'Template updated successfully',
          color: 'green',
        });
      } else {
        // Create new template
        const { error } = await insertData('asset_templates', templatePayload);

        if (error) {
          throw error;
        }

        notifications.show({
          title: 'Success',
          message: 'Template created successfully',
          color: 'green',
        });
      }

      setModalOpened(false);
      fetchTemplates();
    } catch (error) {
      console.error('Error saving template:', error);
      notifications.show({
        title: 'Error',
        message: 'Failed to save template',
        color: 'red',
      });
    }
  };

  const handleDelete = (template: AssetTemplate) => {
    modals.openConfirmModal({
      title: 'Delete Template',
      children: (
        <Text size="sm">
          Are you sure you want to delete the template "{template.name}"? This action cannot be undone.
        </Text>
      ),
      labels: { confirm: 'Delete', cancel: 'Cancel' },
      confirmProps: { color: 'red' },
      onConfirm: async () => {
        try {
          const { error } = await deleteData('asset_templates', {
            column: 'id',
            value: template.id
          });

          if (error) {
            throw error;
          }

          notifications.show({
            title: 'Success',
            message: 'Template deleted successfully',
            color: 'green',
          });

          fetchTemplates();
        } catch (error) {
          console.error('Error deleting template:', error);
          notifications.show({
            title: 'Error',
            message: 'Failed to delete template',
            color: 'red',
          });
        }
      },
    });
  };

  const rows = templates.map((template) => (
    <Table.Tr key={template.id}>
      <Table.Td>
        <Group gap="sm">
          <IconTemplate size={16} />
          <div>
            <Text fw={500}>{template.name}</Text>
            <Text size="xs" c="dimmed">{template.id}</Text>
          </div>
        </Group>
      </Table.Td>
      <Table.Td>
        <Text size="sm">{template.description || 'No description'}</Text>
      </Table.Td>
      <Table.Td>
        <Badge color={getAssetTypeColor(template.asset_type)} variant="light">
          {template.asset_type}
        </Badge>
      </Table.Td>
      <Table.Td>
        <Badge
          color={template.profile_email ? 'blue' : 'gray'}
          variant="light"
          size="sm"
        >
          {template.profile_email ? 'Owner' : 'Admin'}
        </Badge>
        {template.profile_email && (
          <Text size="xs" c="dimmed" mt={2}>
            {template.profile_email}
          </Text>
        )}
      </Table.Td>
      <Table.Td>
        <Badge color={template.is_active ? 'green' : 'red'} variant="light">
          {template.is_active ? 'Active' : 'Inactive'}
        </Badge>
      </Table.Td>
      <Table.Td>
        <Text size="xs" c="dimmed">
          {new Date(template.created_at).toLocaleDateString()}
        </Text>
      </Table.Td>
      <Table.Td>
        <Group gap="xs">
          <Tooltip label="Preview Template">
            <ActionIcon
              variant="subtle"
              color="blue"
              onClick={() => handlePreview(template)}
            >
              <IconEye size={16} />
            </ActionIcon>
          </Tooltip>
          <Tooltip label="Edit Template">
            <ActionIcon
              variant="subtle"
              color="yellow"
              onClick={() => handleEdit(template)}
            >
              <IconEdit size={16} />
            </ActionIcon>
          </Tooltip>
          <Tooltip label="Delete Template">
            <ActionIcon
              variant="subtle"
              color="red"
              onClick={() => handleDelete(template)}
            >
              <IconTrash size={16} />
            </ActionIcon>
          </Tooltip>
        </Group>
      </Table.Td>
    </Table.Tr>
  ));

  return (
    <>
      <Paper withBorder style={{ position: 'relative' }}>
        <LoadingOverlay visible={loading} />
        
        <Group justify="space-between" p="md">
          <div>
            <Text fw={500}>Asset Templates</Text>
            <Text size="sm" c="dimmed">
              Manage predefined templates for asset creation
            </Text>
          </div>
          <Button
            leftSection={<IconPlus size={16} />}
            onClick={handleCreate}
          >
            Create Template
          </Button>
        </Group>

        {templates.length === 0 ? (
          <Alert icon={<IconAlertCircle size={16} />} color="blue" m="md">
            No asset templates found. Create your first template to get started.
          </Alert>
        ) : (
          <ScrollArea>
            <Table striped highlightOnHover>
              <Table.Thead>
                <Table.Tr>
                  <Table.Th>Template</Table.Th>
                  <Table.Th>Description</Table.Th>
                  <Table.Th>Type</Table.Th>
                  <Table.Th>Source</Table.Th>
                  <Table.Th>Status</Table.Th>
                  <Table.Th>Created</Table.Th>
                  <Table.Th>Actions</Table.Th>
                </Table.Tr>
              </Table.Thead>
              <Table.Tbody>{rows}</Table.Tbody>
            </Table>
          </ScrollArea>
        )}
      </Paper>

      {/* Template Form Modal */}
      <Modal
        opened={modalOpened}
        onClose={() => setModalOpened(false)}
        title={editingTemplate ? 'Edit Template' : 'Create Template'}
        size="lg"
      >
        <Stack gap="md">
          <Grid>
            <Grid.Col span={6}>
              <TextInput
                label="Template ID"
                placeholder="e.g., badge_custom_1"
                value={formData.id}
                onChange={(e) => setFormData(prev => ({ ...prev, id: e.target.value }))}
                required
                disabled={!!editingTemplate}
              />
            </Grid.Col>
            <Grid.Col span={6}>
              <Select
                label="Asset Type"
                placeholder="Select asset type"
                data={ASSET_TYPES.map(type => ({ value: type, label: type }))}
                value={formData.asset_type}
                onChange={(value) => setFormData(prev => ({ ...prev, asset_type: value || '' }))}
                required
              />
            </Grid.Col>
          </Grid>

          <TextInput
            label="Template Name"
            placeholder="Enter template name"
            value={formData.name}
            onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
            required
          />

          <Textarea
            label="Description"
            placeholder="Enter template description"
            value={formData.description}
            onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
            minRows={2}
          />

          <Stack gap="xs">
            <Group gap="xs" align="center">
              <Text size="sm" fw={500}>SVG Code</Text>
              <Tooltip label="View available placeholders">
                <ActionIcon
                  size="sm"
                  variant="subtle"
                  color="blue"
                  onClick={() => setPlaceholderInfoModalOpened(true)}
                >
                  <IconInfoCircle size={16} />
                </ActionIcon>
              </Tooltip>
            </Group>
            <Textarea
              placeholder="Enter SVG template code with placeholders like {{receiver_full_name}}, {{issued_date}}, etc."
              value={formData.svg_code}
              onChange={(e) => setFormData(prev => ({ ...prev, svg_code: e.target.value }))}
              minRows={8}
              maxRows={20}
              required
              description="Click the info icon above to see all available placeholders"
            />
          </Stack>

          <Select
            label="Status"
            data={[
              { value: 'true', label: 'Active' },
              { value: 'false', label: 'Inactive' }
            ]}
            value={formData.is_active.toString()}
            onChange={(value) => setFormData(prev => ({ ...prev, is_active: value === 'true' }))}
          />

          <Group justify="flex-end">
            <Button variant="subtle" onClick={() => setModalOpened(false)}>
              Cancel
            </Button>
            <Button onClick={handleSubmit}>
              {editingTemplate ? 'Update' : 'Create'} Template
            </Button>
          </Group>
        </Stack>
      </Modal>

      {/* Template Preview Modal */}
      <Modal
        opened={previewModalOpened}
        onClose={() => setPreviewModalOpened(false)}
        title={`Preview: ${selectedTemplate?.name}`}
        size="lg"
      >
        {selectedTemplate && (
          <Stack gap="md">
            <Card withBorder>
              <Group justify="space-between" mb="md">
                <div>
                  <Text fw={500}>{selectedTemplate.name}</Text>
                  <Text size="sm" c="dimmed">{selectedTemplate.description}</Text>
                </div>
                <Badge color={getAssetTypeColor(selectedTemplate.asset_type)} variant="light">
                  {selectedTemplate.asset_type}
                </Badge>
              </Group>

              <div style={{ textAlign: 'center', marginBottom: '1rem' }}>
                <AssetDisplay
                  imageUrl="/placeholder-image.jpg"
                  templateId={selectedTemplate.id}
                  title="Sample Asset"
                  width={400}
                  height={300}
                  fit="contain"
                  assetData={{
                    title: 'Sample Asset Title',
                    description: 'This is a sample asset description for preview',
                    issued_date: new Date().toLocaleDateString(),
                    expire_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toLocaleDateString(), // 30 days from now
                    receiver_full_name: 'John Doe',
                    issuer_profile_name: 'admin@example',
                    issuer_full_name: 'Admin User',
                    asset_image: '/placeholder-image.jpg',
                    // Keep backward compatibility
                    full_name: 'John Doe',
                    profile_name: 'admin@example'
                  }}
                />
              </div>

              <Text size="sm" fw={500} mb="xs">SVG Template Code:</Text>
              <Textarea
                value={selectedTemplate.svg_code || 'No SVG code available'}
                readOnly
                minRows={8}
                maxRows={15}
                styles={{
                  input: {
                    fontFamily: 'monospace',
                    fontSize: '12px'
                  }
                }}
              />
            </Card>
          </Stack>
        )}
      </Modal>

      {/* SVG Template Placeholders Info Modal */}
      <Modal
        opened={placeholderInfoModalOpened}
        onClose={() => setPlaceholderInfoModalOpened(false)}
        title="SVG Template Placeholders"
        size="lg"
        centered
      >
        <Stack gap="md">
          <Text size="sm" c="dimmed">
            When creating SVG templates for assets, you can use the following placeholders that will be dynamically replaced with actual values:
          </Text>

          <Stack gap="sm">
            <Text size="sm" fw={500} c="blue">Asset Information:</Text>
            <Stack gap="xs" pl="md">
              <Group justify="space-between">
                <Text size="sm" style={{ fontFamily: 'monospace' }} c="blue">{'{{asset_title}}'}</Text>
                <Text size="sm" c="dimmed">Asset title</Text>
              </Group>
              <Group justify="space-between">
                <Text size="sm" style={{ fontFamily: 'monospace' }} c="blue">{'{{asset_description}}'}</Text>
                <Text size="sm" c="dimmed">Asset description</Text>
              </Group>
              <Group justify="space-between">
                <Text size="sm" style={{ fontFamily: 'monospace' }} c="blue">{'{{asset_image}}'}</Text>
                <Text size="sm" c="dimmed">Asset image URL</Text>
              </Group>
            </Stack>

            <Text size="sm" fw={500} c="green">Date Information:</Text>
            <Stack gap="xs" pl="md">
              <Group justify="space-between">
                <Text size="sm" style={{ fontFamily: 'monospace' }} c="green">{'{{issued_date}}'}</Text>
                <Text size="sm" c="dimmed">Date when asset was issued</Text>
              </Group>
              <Group justify="space-between">
                <Text size="sm" style={{ fontFamily: 'monospace' }} c="green">{'{{expire_date}}'}</Text>
                <Text size="sm" c="dimmed">Asset expiration date</Text>
              </Group>
            </Stack>

            <Text size="sm" fw={500} c="violet">Recipient Information:</Text>
            <Stack gap="xs" pl="md">
              <Group justify="space-between">
                <Text size="sm" style={{ fontFamily: 'monospace' }} c="violet">{'{{receiver_full_name}}'}</Text>
                <Text size="sm" c="dimmed">Recipient's full name</Text>
              </Group>
              <Group justify="space-between">
                <Text size="sm" style={{ fontFamily: 'monospace' }} c="violet">{'{{receiver_profile_avatar}}'}</Text>
                <Text size="sm" c="dimmed">Recipient's profile avatar URL</Text>
              </Group>
            </Stack>

            <Text size="sm" fw={500} c="orange">Issuer Information:</Text>
            <Stack gap="xs" pl="md">
              <Group justify="space-between">
                <Text size="sm" style={{ fontFamily: 'monospace' }} c="orange">{'{{issuer_profile_name}}'}</Text>
                <Text size="sm" c="dimmed">Issuer's ODude name</Text>
              </Group>
              <Group justify="space-between">
                <Text size="sm" style={{ fontFamily: 'monospace' }} c="orange">{'{{issuer_full_name}}'}</Text>
                <Text size="sm" c="dimmed">Issuer's full name</Text>
              </Group>
              <Group justify="space-between">
                <Text size="sm" style={{ fontFamily: 'monospace' }} c="orange">{'{{issuer_profile_avatar}}'}</Text>
                <Text size="sm" c="dimmed">Issuer's profile avatar URL</Text>
              </Group>
            </Stack>

            <Text size="sm" fw={500} c="red">Deprecated (still supported):</Text>
            <Stack gap="xs" pl="md">
              <Group justify="space-between">
                <Text size="sm" style={{ fontFamily: 'monospace' }} c="red">{'{{full_name}}'}</Text>
                <Text size="sm" c="dimmed">Use {'{{receiver_full_name}}'} instead</Text>
              </Group>
              <Group justify="space-between">
                <Text size="sm" style={{ fontFamily: 'monospace' }} c="red">{'{{profile_name}}'}</Text>
                <Text size="sm" c="dimmed">Use {'{{issuer_profile_name}}'} instead</Text>
              </Group>
              <Group justify="space-between">
                <Text size="sm" style={{ fontFamily: 'monospace' }} c="red">{'{{profile_avatar}}'}</Text>
                <Text size="sm" c="dimmed">Use {'{{issuer_profile_avatar}}'} instead</Text>
              </Group>
            </Stack>
          </Stack>

          <Alert color="blue" variant="light">
            <Text size="sm" fw={500}>Example usage:</Text>
            <Text size="sm" style={{ fontFamily: 'monospace' }}>
              {'<text x="50" y="100">Congratulations {{receiver_full_name}}!</text>'}
            </Text>
          </Alert>

          <Group justify="flex-end">
            <Button onClick={() => setPlaceholderInfoModalOpened(false)}>
              Got it
            </Button>
          </Group>
        </Stack>
      </Modal>
    </>
  );
}
