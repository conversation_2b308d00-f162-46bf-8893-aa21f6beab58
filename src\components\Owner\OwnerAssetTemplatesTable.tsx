'use client';

import { useState, useEffect } from 'react';
import {
  Table,
  Text,
  Badge,
  Group,
  ActionIcon,
  Button,
  Modal,
  TextInput,
  Textarea,
  Select,
  Stack,
  Paper,
  Card,
  Grid,
  Alert,
  LoadingOverlay,
  ScrollArea,
  Tooltip
} from '@mantine/core';
import {
  IconEdit,
  IconTrash,
  IconPlus,
  IconEye,
  IconTemplate,
  IconAlertCircle,
  IconInfoCircle
} from '@tabler/icons-react';
import { notifications } from '@mantine/notifications';
import { modals } from '@mantine/modals';
import { AssetTemplate, ASSET_TYPES, getAssetTypeColor } from '../../lib/assets-utils';
import AssetDisplay from '../Assets/AssetDisplay';

interface TemplateFormData {
  id: string;
  name: string;
  description: string;
  asset_type: string;
  svg_code: string;
  is_active: boolean;
}

interface OwnerAssetTemplatesTableProps {
  refreshTrigger?: number;
}

export function OwnerAssetTemplatesTable({ refreshTrigger }: OwnerAssetTemplatesTableProps) {
  const [templates, setTemplates] = useState<AssetTemplate[]>([]);
  const [loading, setLoading] = useState(true);
  const [modalOpened, setModalOpened] = useState(false);
  const [previewModalOpened, setPreviewModalOpened] = useState(false);
  const [placeholderInfoModalOpened, setPlaceholderInfoModalOpened] = useState(false);
  const [editingTemplate, setEditingTemplate] = useState<AssetTemplate | null>(null);
  const [selectedTemplate, setSelectedTemplate] = useState<AssetTemplate | null>(null);
  const [formData, setFormData] = useState<TemplateFormData>({
    id: '',
    name: '',
    description: '',
    asset_type: '',
    svg_code: '',
    is_active: true
  });

  useEffect(() => {
    fetchTemplates();
  }, [refreshTrigger]);

  const fetchTemplates = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/asset-templates?owner=true');
      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to fetch templates');
      }

      setTemplates(result.templates || []);
    } catch (error) {
      console.error('Error fetching templates:', error);
      notifications.show({
        title: 'Error',
        message: 'Failed to fetch asset templates',
        color: 'red',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCreate = () => {
    setEditingTemplate(null);
    setFormData({
      id: '',
      name: '',
      description: '',
      asset_type: '',
      svg_code: '',
      is_active: true
    });
    setModalOpened(true);
  };

  const handleEdit = (template: AssetTemplate) => {
    setEditingTemplate(template);
    setFormData({
      id: template.id,
      name: template.name,
      description: template.description || '',
      asset_type: template.asset_type,
      svg_code: template.svg_code || '',
      is_active: template.is_active
    });
    setModalOpened(true);
  };

  const handlePreview = (template: AssetTemplate) => {
    setSelectedTemplate(template);
    setPreviewModalOpened(true);
  };

  const handleSubmit = async () => {
    try {
      // Validate form data
      if (!formData.id || !formData.name || !formData.asset_type || !formData.svg_code) {
        notifications.show({
          title: 'Validation Error',
          message: 'Please fill in all required fields (ID, Name, Asset Type, and SVG Code)',
          color: 'red',
        });
        return;
      }

      // Basic SVG validation
      if (!formData.svg_code.trim().startsWith('<svg')) {
        notifications.show({
          title: 'Validation Error',
          message: 'SVG Code must start with <svg tag',
          color: 'red',
        });
        return;
      }

      const templatePayload = {
        id: formData.id,
        name: formData.name,
        description: formData.description || null,
        asset_type: formData.asset_type,
        svg_code: formData.svg_code,
        is_active: formData.is_active
      };

      let response;
      if (editingTemplate) {
        // Update existing template
        response = await fetch('/api/asset-templates', {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(templatePayload),
        });
      } else {
        // Create new template
        response = await fetch('/api/asset-templates', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(templatePayload),
        });
      }

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to save template');
      }

      notifications.show({
        title: 'Success',
        message: editingTemplate ? 'Template updated successfully' : 'Template created successfully',
        color: 'green',
      });

      setModalOpened(false);
      fetchTemplates();
    } catch (error) {
      console.error('Error saving template:', error);
      notifications.show({
        title: 'Error',
        message: error instanceof Error ? error.message : 'Failed to save template',
        color: 'red',
      });
    }
  };

  const handleDelete = (template: AssetTemplate) => {
    modals.openConfirmModal({
      title: 'Delete Template',
      children: (
        <Stack gap="sm">
          <Text size="sm">
            Are you sure you want to delete the template "{template.name}"?
          </Text>
          <Alert color="orange" variant="light">
            <Text size="sm" fw={500}>⚠️ Warning:</Text>
            <Text size="sm">
              This will also delete ALL assets created with this template and their associated transfers. 
              This action cannot be undone.
            </Text>
          </Alert>
        </Stack>
      ),
      labels: { confirm: 'Delete', cancel: 'Cancel' },
      confirmProps: { color: 'red' },
      onConfirm: async () => {
        try {
          const response = await fetch(`/api/asset-templates?id=${template.id}`, {
            method: 'DELETE',
          });

          const result = await response.json();

          if (!response.ok) {
            throw new Error(result.error || 'Failed to delete template');
          }

          notifications.show({
            title: 'Success',
            message: result.message || 'Template deleted successfully',
            color: 'green',
          });

          fetchTemplates();
        } catch (error) {
          console.error('Error deleting template:', error);
          notifications.show({
            title: 'Error',
            message: error instanceof Error ? error.message : 'Failed to delete template',
            color: 'red',
          });
        }
      },
    });
  };

  if (loading) {
    return <LoadingOverlay visible />;
  }

  return (
    <Stack gap="md">
      <Group justify="space-between">
        <Group>
          <IconTemplate size={20} />
          <Text size="lg" fw={600}>Asset Templates</Text>
          <Badge variant="light" color="blue">{templates.length}</Badge>
        </Group>
        <Button
          leftSection={<IconPlus size={16} />}
          onClick={handleCreate}
          variant="filled"
        >
          Create Template
        </Button>
      </Group>

      {templates.length === 0 ? (
        <Paper withBorder p="xl" style={{ textAlign: 'center' }}>
          <IconTemplate size={48} style={{ opacity: 0.3, marginBottom: 16 }} />
          <Text size="lg" fw={500} c="dimmed" mb="xs">
            No Asset Templates
          </Text>
          <Text size="sm" c="dimmed" mb="lg">
            Create your first asset template to get started
          </Text>
          <Button
            leftSection={<IconPlus size={16} />}
            onClick={handleCreate}
            variant="light"
          >
            Create Template
          </Button>
        </Paper>
      ) : (
        <ScrollArea>
          <Table striped highlightOnHover>
            <Table.Thead>
              <Table.Tr>
                <Table.Th>Template</Table.Th>
                <Table.Th>Type</Table.Th>
                <Table.Th>Status</Table.Th>
                <Table.Th>Created</Table.Th>
                <Table.Th>Actions</Table.Th>
              </Table.Tr>
            </Table.Thead>
            <Table.Tbody>
              {templates.map((template) => (
                <Table.Tr key={template.id}>
                  <Table.Td>
                    <div>
                      <Text fw={500} size="sm">{template.name}</Text>
                      <Text size="xs" c="dimmed">{template.id}</Text>
                      {template.description && (
                        <Text size="xs" c="dimmed" lineClamp={1}>
                          {template.description}
                        </Text>
                      )}
                    </div>
                  </Table.Td>
                  
                  <Table.Td>
                    <Badge color={getAssetTypeColor(template.asset_type)} variant="light" size="sm">
                      {template.asset_type}
                    </Badge>
                  </Table.Td>
                  
                  <Table.Td>
                    <Badge 
                      color={template.is_active ? 'green' : 'gray'} 
                      variant="light" 
                      size="sm"
                    >
                      {template.is_active ? 'Active' : 'Inactive'}
                    </Badge>
                  </Table.Td>
                  
                  <Table.Td>
                    <Text size="sm">
                      {new Date(template.created_at).toLocaleDateString()}
                    </Text>
                  </Table.Td>
                  
                  <Table.Td>
                    <Group gap="xs">
                      <Tooltip label="Preview Template">
                        <ActionIcon
                          variant="subtle"
                          color="blue"
                          onClick={() => handlePreview(template)}
                        >
                          <IconEye size={16} />
                        </ActionIcon>
                      </Tooltip>
                      
                      <Tooltip label="Edit Template">
                        <ActionIcon
                          variant="subtle"
                          color="yellow"
                          onClick={() => handleEdit(template)}
                        >
                          <IconEdit size={16} />
                        </ActionIcon>
                      </Tooltip>
                      
                      <Tooltip label="Delete Template">
                        <ActionIcon
                          variant="subtle"
                          color="red"
                          onClick={() => handleDelete(template)}
                        >
                          <IconTrash size={16} />
                        </ActionIcon>
                      </Tooltip>
                    </Group>
                  </Table.Td>
                </Table.Tr>
              ))}
            </Table.Tbody>
          </Table>
        </ScrollArea>
      )}

      {/* Template Form Modal */}
      <Modal
        opened={modalOpened}
        onClose={() => setModalOpened(false)}
        title={editingTemplate ? 'Edit Template' : 'Create Template'}
        size="lg"
      >
        <Stack gap="md">
          <Grid>
            <Grid.Col span={6}>
              <TextInput
                label="Template ID"
                placeholder="e.g., badge_custom_1"
                value={formData.id}
                onChange={(e) => setFormData(prev => ({ ...prev, id: e.target.value }))}
                required
                disabled={!!editingTemplate}
              />
            </Grid.Col>
            <Grid.Col span={6}>
              <Select
                label="Asset Type"
                placeholder="Select asset type"
                data={ASSET_TYPES.map(type => ({ value: type, label: type }))}
                value={formData.asset_type}
                onChange={(value) => setFormData(prev => ({ ...prev, asset_type: value || '' }))}
                required
              />
            </Grid.Col>
          </Grid>

          <TextInput
            label="Template Name"
            placeholder="Enter template name"
            value={formData.name}
            onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
            required
          />

          <Textarea
            label="Description"
            placeholder="Enter template description (optional)"
            value={formData.description}
            onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
            minRows={2}
          />

          <Stack gap="xs">
            <Group gap="xs" align="center">
              <Text size="sm" fw={500}>SVG Code</Text>
              <Tooltip label="View available placeholders">
                <ActionIcon
                  size="sm"
                  variant="subtle"
                  color="blue"
                  onClick={() => setPlaceholderInfoModalOpened(true)}
                >
                  <IconInfoCircle size={16} />
                </ActionIcon>
              </Tooltip>
            </Group>
            <Textarea
              placeholder="Enter SVG template code with placeholders like {{receiver_full_name}}, {{issued_date}}, etc."
              value={formData.svg_code}
              onChange={(e) => setFormData(prev => ({ ...prev, svg_code: e.target.value }))}
              minRows={8}
              maxRows={20}
              required
              description="Click the info icon above to see all available placeholders"
            />
          </Stack>

          <Group justify="flex-end">
            <Button variant="outline" onClick={() => setModalOpened(false)}>
              Cancel
            </Button>
            <Button onClick={handleSubmit}>
              {editingTemplate ? 'Update Template' : 'Create Template'}
            </Button>
          </Group>
        </Stack>
      </Modal>

      {/* Template Preview Modal */}
      <Modal
        opened={previewModalOpened}
        onClose={() => setPreviewModalOpened(false)}
        title="Template Preview"
        size="lg"
      >
        {selectedTemplate && (
          <Stack gap="md">
            <Group>
              <Badge color={getAssetTypeColor(selectedTemplate.asset_type)} variant="light">
                {selectedTemplate.asset_type}
              </Badge>
              <Badge color={selectedTemplate.is_active ? 'green' : 'gray'} variant="light">
                {selectedTemplate.is_active ? 'Active' : 'Inactive'}
              </Badge>
            </Group>

            <Card withBorder>
              <Text size="sm" fw={500} mb="xs">Template Information:</Text>
              <Text size="sm"><strong>ID:</strong> {selectedTemplate.id}</Text>
              <Text size="sm"><strong>Name:</strong> {selectedTemplate.name}</Text>
              {selectedTemplate.description && (
                <Text size="sm"><strong>Description:</strong> {selectedTemplate.description}</Text>
              )}
              <Text size="sm"><strong>Created:</strong> {new Date(selectedTemplate.created_at).toLocaleString()}</Text>
            </Card>

            <Card withBorder>
              <Text size="sm" fw={500} mb="xs">SVG Template Code:</Text>
              <Textarea
                value={selectedTemplate.svg_code || 'No SVG code available'}
                readOnly
                minRows={8}
                maxRows={15}
                styles={{
                  input: {
                    fontFamily: 'monospace',
                    fontSize: '12px'
                  }
                }}
              />
            </Card>
          </Stack>
        )}
      </Modal>

      {/* SVG Template Placeholders Info Modal */}
      <Modal
        opened={placeholderInfoModalOpened}
        onClose={() => setPlaceholderInfoModalOpened(false)}
        title="SVG Template Placeholders"
        size="lg"
        centered
      >
        <Stack gap="md">
          <Text size="sm" c="dimmed">
            When creating SVG templates for assets, you can use the following placeholders that will be dynamically replaced with actual values:
          </Text>

          <Stack gap="sm">
            <Text size="sm" fw={500} c="blue">Asset Information:</Text>
            <Stack gap="xs" pl="md">
              <Group justify="space-between">
                <Text size="sm" style={{ fontFamily: 'monospace' }} c="blue">{'{{asset_title}}'}</Text>
                <Text size="sm" c="dimmed">Asset title</Text>
              </Group>
              <Group justify="space-between">
                <Text size="sm" style={{ fontFamily: 'monospace' }} c="blue">{'{{asset_description}}'}</Text>
                <Text size="sm" c="dimmed">Asset description</Text>
              </Group>
              <Group justify="space-between">
                <Text size="sm" style={{ fontFamily: 'monospace' }} c="blue">{'{{asset_image}}'}</Text>
                <Text size="sm" c="dimmed">Asset image URL</Text>
              </Group>
            </Stack>

            <Text size="sm" fw={500} c="green">Date Information:</Text>
            <Stack gap="xs" pl="md">
              <Group justify="space-between">
                <Text size="sm" style={{ fontFamily: 'monospace' }} c="green">{'{{issued_date}}'}</Text>
                <Text size="sm" c="dimmed">Date when asset was issued</Text>
              </Group>
              <Group justify="space-between">
                <Text size="sm" style={{ fontFamily: 'monospace' }} c="green">{'{{expire_date}}'}</Text>
                <Text size="sm" c="dimmed">Asset expiration date</Text>
              </Group>
            </Stack>

            <Text size="sm" fw={500} c="violet">Recipient Information:</Text>
            <Stack gap="xs" pl="md">
              <Group justify="space-between">
                <Text size="sm" style={{ fontFamily: 'monospace' }} c="violet">{'{{receiver_full_name}}'}</Text>
                <Text size="sm" c="dimmed">Recipient's full name</Text>
              </Group>
              <Group justify="space-between">
                <Text size="sm" style={{ fontFamily: 'monospace' }} c="violet">{'{{receiver_profile_avatar}}'}</Text>
                <Text size="sm" c="dimmed">Recipient's profile avatar URL</Text>
              </Group>
            </Stack>

            <Text size="sm" fw={500} c="orange">Issuer Information:</Text>
            <Stack gap="xs" pl="md">
              <Group justify="space-between">
                <Text size="sm" style={{ fontFamily: 'monospace' }} c="orange">{'{{issuer_profile_name}}'}</Text>
                <Text size="sm" c="dimmed">Issuer's ODude name</Text>
              </Group>
              <Group justify="space-between">
                <Text size="sm" style={{ fontFamily: 'monospace' }} c="orange">{'{{issuer_full_name}}'}</Text>
                <Text size="sm" c="dimmed">Issuer's full name</Text>
              </Group>
              <Group justify="space-between">
                <Text size="sm" style={{ fontFamily: 'monospace' }} c="orange">{'{{issuer_profile_avatar}}'}</Text>
                <Text size="sm" c="dimmed">Issuer's profile avatar URL</Text>
              </Group>
            </Stack>

            <Text size="sm" fw={500} c="red">Deprecated (still supported):</Text>
            <Stack gap="xs" pl="md">
              <Group justify="space-between">
                <Text size="sm" style={{ fontFamily: 'monospace' }} c="red">{'{{full_name}}'}</Text>
                <Text size="sm" c="dimmed">Use {'{{receiver_full_name}}'} instead</Text>
              </Group>
              <Group justify="space-between">
                <Text size="sm" style={{ fontFamily: 'monospace' }} c="red">{'{{profile_name}}'}</Text>
                <Text size="sm" c="dimmed">Use {'{{issuer_profile_name}}'} instead</Text>
              </Group>
              <Group justify="space-between">
                <Text size="sm" style={{ fontFamily: 'monospace' }} c="red">{'{{profile_avatar}}'}</Text>
                <Text size="sm" c="dimmed">Use {'{{issuer_profile_avatar}}'} instead</Text>
              </Group>
            </Stack>
          </Stack>

          <Alert color="blue" variant="light">
            <Text size="sm" fw={500}>Example usage:</Text>
            <Text size="sm" style={{ fontFamily: 'monospace' }}>
              {'<text x="50" y="100">Congratulations {{receiver_full_name}}!</text>'}
            </Text>
          </Alert>

          <Group justify="flex-end">
            <Button onClick={() => setPlaceholderInfoModalOpened(false)}>
              Got it
            </Button>
          </Group>
        </Stack>
      </Modal>
    </Stack>
  );
}
