-- Fresh database setup for odude application
-- This script creates all necessary tables and functions from scratch

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create user_points table to track user point balances
CREATE TABLE IF NOT EXISTS user_points (
  email TEXT PRIMARY KEY,
  points INTEGER DEFAULT 0 NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  CONSTRAINT points_non_negative CHECK (points >= 0)
);

-- Create transaction_logs table to track point transactions (FIXED: Added from_email and to_email columns)
CREATE TABLE IF NOT EXISTS transaction_logs (
  id SERIAL PRIMARY KEY,
  email TEXT NOT NULL,
  transaction_type TEXT NOT NULL,
  points_change INTEGER NOT NULL,
  points_before INTEGER NOT NULL,
  points_after INTEGER NOT NULL,
  description TEXT,
  reference_id TEXT,
  from_email TEXT,
  to_email TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create contact table for contact management
CREATE TABLE IF NOT EXISTS contact (
  timestamp TIMESTAMPTZ DEFAULT NOW(),
  name TEXT PRIMARY KEY,
  image TEXT,
  description TEXT,
  uri TEXT,
  profile TEXT,
  email TEXT,
  website TEXT,
  phone TEXT,
  tg_bot TEXT,
  notes JSONB,
  web2 TEXT,
  web3 TEXT,
  links JSONB,
  images JSONB,
  social JSONB,
  crypto JSONB,
  extra JSONB,
  profile_email TEXT,
  minted TEXT,
  disabled BOOLEAN DEFAULT FALSE
);

-- Create profiles table for user profiles
CREATE TABLE IF NOT EXISTS profiles (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email TEXT UNIQUE NOT NULL,
  full_name TEXT,
  avatar_url TEXT,
  points INTEGER DEFAULT 2000,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  disabled BOOLEAN DEFAULT FALSE
);

-- Create settings table for user settings
CREATE TABLE IF NOT EXISTS settings (
  email TEXT PRIMARY KEY,
  max_contact_limit INTEGER DEFAULT 4,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create bookmark table for bookmarks
CREATE TABLE IF NOT EXISTS bookmark (
  id BIGSERIAL PRIMARY KEY,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  contact_name VARCHAR,
  contact_email VARCHAR
);

-- Create primary_name_owners table for ownership management
CREATE TABLE IF NOT EXISTS primary_name_owners (
  id SERIAL PRIMARY KEY,
  user_email TEXT NOT NULL,
  owner_of TEXT NOT NULL,
  ownership_type TEXT DEFAULT 'static' CHECK (ownership_type IN ('static', 'dynamic')),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(user_email, owner_of)
);

-- Create user_qr_services table for QR services
CREATE TABLE IF NOT EXISTS user_qr_services (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  contact_name TEXT NOT NULL,
  service_name TEXT NOT NULL,
  service_data JSONB DEFAULT '{}',
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create asset_templates table for asset templates
CREATE TABLE IF NOT EXISTS asset_templates (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  asset_type TEXT NOT NULL CHECK (asset_type IN ('Badge', 'Certificate', 'Ticket', 'Coupon')),
  svg_code TEXT NOT NULL,
  is_active BOOLEAN DEFAULT TRUE,
  profile_email TEXT, -- NULL for admin templates, email for owner-created templates
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create assets table for asset management
CREATE TABLE IF NOT EXISTS assets (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  title TEXT NOT NULL,
  description TEXT,
  asset_type TEXT NOT NULL CHECK (asset_type IN ('Badge', 'Certificate', 'Ticket', 'Coupon')),
  image_url TEXT NOT NULL,
  template_id TEXT,
  issuer_odude_name TEXT NOT NULL,
  issuer_email TEXT NOT NULL,
  expiry_date TIMESTAMPTZ,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  is_deleted BOOLEAN DEFAULT FALSE,
  FOREIGN KEY (template_id) REFERENCES asset_templates(id) ON DELETE SET NULL
);

-- Create asset_transfers table for asset transfers
CREATE TABLE IF NOT EXISTS asset_transfers (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  asset_id UUID NOT NULL,
  from_odude_name TEXT NOT NULL,
  from_email TEXT NOT NULL,
  to_odude_name TEXT NOT NULL,
  to_email TEXT,
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'declined', 'hidden')),
  transferred_at TIMESTAMPTZ DEFAULT NOW(),
  responded_at TIMESTAMPTZ,
  response_note TEXT,
  FOREIGN KEY (asset_id) REFERENCES assets(id)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_transaction_logs_email ON transaction_logs (email);
CREATE INDEX IF NOT EXISTS idx_transaction_logs_created_at ON transaction_logs (created_at);
CREATE INDEX IF NOT EXISTS idx_transaction_logs_from_email ON transaction_logs (from_email);
CREATE INDEX IF NOT EXISTS idx_transaction_logs_to_email ON transaction_logs (to_email);
CREATE INDEX IF NOT EXISTS idx_contact_profile_email ON contact (profile_email);
CREATE INDEX IF NOT EXISTS idx_contact_name_lower ON contact (LOWER(name));
CREATE INDEX IF NOT EXISTS idx_primary_name_owners_email ON primary_name_owners (user_email);
CREATE INDEX IF NOT EXISTS idx_primary_name_owners_owner_of ON primary_name_owners (owner_of);
CREATE INDEX IF NOT EXISTS idx_assets_issuer_email ON assets (issuer_email);
CREATE INDEX IF NOT EXISTS idx_asset_transfers_asset_id ON asset_transfers (asset_id);

-- Add foreign key constraints
ALTER TABLE transaction_logs ADD CONSTRAINT fk_transaction_logs_email 
  FOREIGN KEY (email) REFERENCES user_points(email) ON DELETE CASCADE;

-- Add comments for documentation
COMMENT ON TABLE user_points IS 'Tracks user point balances for the reward system';
COMMENT ON TABLE transaction_logs IS 'Logs all point transactions for audit trail';
COMMENT ON TABLE contact IS 'Stores contact information and profiles';
COMMENT ON TABLE profiles IS 'User profile information';
COMMENT ON TABLE primary_name_owners IS 'Manages ownership of primary names/TLNs';
COMMENT ON TABLE assets IS 'Digital assets (badges, certificates, tickets, coupons)';
COMMENT ON TABLE asset_transfers IS 'Tracks asset transfers between users';

COMMENT ON COLUMN transaction_logs.email IS 'User email address';
COMMENT ON COLUMN transaction_logs.transaction_type IS 'Type of transaction (SIGNUP, CREATE_CONTACT, TRANSFER_SEND, TRANSFER_RECEIVE, ADMIN_LOAD, ADMIN_UNLOAD)';
COMMENT ON COLUMN transaction_logs.points_change IS 'Points added (positive) or deducted (negative)';
COMMENT ON COLUMN transaction_logs.points_before IS 'Point balance before transaction';
COMMENT ON COLUMN transaction_logs.points_after IS 'Point balance after transaction';
COMMENT ON COLUMN transaction_logs.from_email IS 'Email of user sending points (for transfers)';
COMMENT ON COLUMN transaction_logs.to_email IS 'Email of user receiving points (for transfers)';

-- Create function to update user points with transaction logging
CREATE OR REPLACE FUNCTION update_user_points(
    user_email TEXT,
    points_change INTEGER,
    transaction_type TEXT,
    description TEXT DEFAULT NULL,
    reference_id TEXT DEFAULT NULL,
    from_email TEXT DEFAULT NULL,
    to_email TEXT DEFAULT NULL
)
RETURNS BOOLEAN AS $$
DECLARE
    current_points INTEGER;
    new_points INTEGER;
BEGIN
    -- Get current points or create user if doesn't exist
    INSERT INTO user_points (email, points)
    VALUES (user_email, 0)
    ON CONFLICT (email) DO NOTHING;

    SELECT points INTO current_points
    FROM user_points
    WHERE email = user_email;

    -- Calculate new points
    new_points := current_points + points_change;

    -- Check if user has enough points for deduction
    IF new_points < 0 THEN
        RETURN FALSE;
    END IF;

    -- Update user points
    UPDATE user_points
    SET points = new_points, updated_at = NOW()
    WHERE email = user_email;

    -- Log the transaction
    INSERT INTO transaction_logs (
        email,
        transaction_type,
        points_change,
        points_before,
        points_after,
        description,
        reference_id,
        from_email,
        to_email
    ) VALUES (
        user_email,
        transaction_type,
        points_change,
        current_points,
        new_points,
        description,
        reference_id,
        from_email,
        to_email
    );

    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- Create function to update user points without transaction logging (for bookmarks)
CREATE OR REPLACE FUNCTION update_user_points_only(
    user_email TEXT,
    points_change INTEGER
)
RETURNS BOOLEAN AS $$
DECLARE
    current_points INTEGER;
    new_points INTEGER;
BEGIN
    -- Get current points or create user if doesn't exist
    INSERT INTO user_points (email, points)
    VALUES (user_email, 0)
    ON CONFLICT (email) DO NOTHING;

    SELECT points INTO current_points
    FROM user_points
    WHERE email = user_email;

    -- Calculate new points
    new_points := current_points + points_change;

    -- Check if user has enough points for deduction
    IF new_points < 0 THEN
        RETURN FALSE;
    END IF;

    -- Update user points
    UPDATE user_points
    SET points = new_points, updated_at = NOW()
    WHERE email = user_email;

    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- Create function specifically for point transfers
CREATE OR REPLACE FUNCTION transfer_points(
    sender_email TEXT,
    recipient_email TEXT,
    points_amount INTEGER,
    description TEXT DEFAULT NULL
)
RETURNS BOOLEAN AS $$
DECLARE
    sender_current_points INTEGER;
    transfer_id TEXT;
BEGIN
    -- Generate a unique transfer ID
    transfer_id := 'TRANSFER_' || extract(epoch from now())::text || '_' || floor(random() * 1000)::text;

    -- Check if sender has enough points
    SELECT points INTO sender_current_points
    FROM user_points
    WHERE email = sender_email;

    IF sender_current_points IS NULL OR sender_current_points < points_amount THEN
        RETURN FALSE;
    END IF;

    -- Check if recipient exists in user_points (has a profile)
    IF NOT EXISTS (SELECT 1 FROM user_points WHERE email = recipient_email) THEN
        RETURN FALSE;
    END IF;

    -- Deduct points from sender
    IF NOT update_user_points(
        sender_email,
        -points_amount,
        'TRANSFER_SEND',
        COALESCE(description, 'Points transferred to ' || recipient_email),
        transfer_id,
        sender_email,
        recipient_email
    ) THEN
        RETURN FALSE;
    END IF;

    -- Add points to recipient
    IF NOT update_user_points(
        recipient_email,
        points_amount,
        'TRANSFER_RECEIVE',
        COALESCE(description, 'Points received from ' || sender_email),
        transfer_id,
        sender_email,
        recipient_email
    ) THEN
        -- Rollback sender transaction if recipient transaction fails
        PERFORM update_user_points(
            sender_email,
            points_amount,
            'TRANSFER_ROLLBACK',
            'Transfer failed - points restored',
            transfer_id,
            sender_email,
            recipient_email
        );
        RETURN FALSE;
    END IF;

    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- Create view for assets with transfer statistics
CREATE OR REPLACE VIEW assets_with_stats AS
SELECT
    a.*,
    COALESCE(transfer_stats.total_transfers, 0) as total_transfers,
    COALESCE(transfer_stats.pending_transfers, 0) as pending_transfers,
    COALESCE(transfer_stats.approved_transfers, 0) as approved_transfers
FROM assets a
LEFT JOIN (
    SELECT
        asset_id,
        COUNT(*) as total_transfers,
        COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_transfers,
        COUNT(CASE WHEN status = 'approved' THEN 1 END) as approved_transfers
    FROM asset_transfers
    GROUP BY asset_id
) transfer_stats ON a.id = transfer_stats.asset_id
WHERE a.is_deleted = FALSE;
