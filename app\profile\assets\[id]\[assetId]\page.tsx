'use client';

import { useState, useEffect } from 'react';
import { useParams, notFound } from 'next/navigation';
import {
  Title,
  Text,
  Stack,
  Group,
  Badge,
  Alert,
  LoadingOverlay,
  Avatar,
  Button,
  Card,
  Anchor,
  Box,
  ThemeIcon
} from '@mantine/core';
import {
  IconTrophy,
  IconCalendar,
  IconUser,
  IconAlertCircle,
  IconArrowLeft,
  IconExternalLink
} from '@tabler/icons-react';
import { fetchData } from 'src/lib/supabase';
import FullLayout from 'src/components/layouts/FullLayout';
import { getImage, ImageData } from 'src/lib/common';
import classes from 'src/components/Card/contact.module.css';
import AssetDisplay from 'src/components/Assets/AssetDisplay';
import { getAssetTypeColor } from 'src/lib/assets-utils';
import { useSession } from 'next-auth/react';

interface AssetWithTransfer {
  id: string;
  title: string;
  description: string | null;
  asset_type: 'Badge' | 'Certificate' | 'Ticket' | 'Coupon';
  image_url: string;
  template_id: string | null;
  issuer_odude_name: string;
  issuer_email: string;
  expiry_date: string | null;
  metadata: any;
  created_at: string;
  transfer?: {
    id: string;
    status: string;
    transferred_at: string;
    to_odude_name: string;
    to_email: string;
  };
}

interface UserInfo {
  profile: string;
  email: string;
  images?: ImageData;
  description?: string;
}

interface IssuerInfo {
  profile: string;
  email: string;
  images?: ImageData;
  description?: string;
}

export default function AssetDetailPage() {
  const params = useParams();
  const { data: session } = useSession();
  const rawId = params?.id as string;
  const assetId = params?.assetId as string;
  const odudeName = rawId && rawId.includes('%') ? decodeURIComponent(rawId).toLowerCase() : rawId?.toLowerCase();

  const [asset, setAsset] = useState<AssetWithTransfer | null>(null);
  const [userInfo, setUserInfo] = useState<UserInfo | null>(null);
  const [issuerInfo, setIssuerInfo] = useState<IssuerInfo | null>(null);
  const [loading, setLoading] = useState(true);
  const [canView, setCanView] = useState(false);

  useEffect(() => {
    if (odudeName && assetId) {
      fetchAssetData();
      fetchUserInfo();
    }
  }, [odudeName, assetId]);

  const fetchUserInfo = async () => {
    if (!odudeName) return;

    try {
      const { data, error } = await fetchData('contact', {
        select: 'profile, email, images, description',
        filter: [{ column: 'name', value: odudeName.toLowerCase() }],
        single: true
      });

      if (!error && data) {
        const userData = Array.isArray(data) ? data[0] : data;
        setUserInfo(userData);
      }
    } catch (error) {
      console.error('Error fetching user info:', error);
    }
  };

  const fetchIssuerInfo = async (issuerOdudeName: string) => {
    try {
      const { data, error } = await fetchData('contact', {
        select: 'profile, email, images, description',
        filter: [{ column: 'name', value: issuerOdudeName.toLowerCase() }],
        single: true
      });

      if (!error && data) {
        const issuerData = Array.isArray(data) ? data[0] : data;
        setIssuerInfo(issuerData);
      }
    } catch (error) {
      console.error('Error fetching issuer info:', error);
    }
  };

  const fetchAssetData = async () => {
    if (!odudeName || !assetId) return;

    try {
      setLoading(true);

      // First, try to fetch the asset with transfer information
      const { data: transferData, error: transferError } = await fetchData('asset_transfers', {
        select: `
          *,
          assets (
            id,
            title,
            description,
            asset_type,
            image_url,
            template_id,
            issuer_odude_name,
            issuer_email,
            expiry_date,
            metadata,
            created_at
          )
        `,
        filter: [
          { column: 'asset_id', value: assetId },
          { column: 'to_odude_name', value: odudeName }
        ],
        single: true
      });

      if (!transferError && transferData && transferData.assets) {
        const assetWithTransfer: AssetWithTransfer = {
          ...transferData.assets,
          transfer: {
            id: transferData.id,
            status: transferData.status,
            transferred_at: transferData.transferred_at,
            to_odude_name: transferData.to_odude_name,
            to_email: transferData.to_email
          }
        };

        // Check access permissions
        const isLoggedInAsRecipient = session?.user?.email === transferData.to_email;
        const isApprovedAsset = transferData.status === 'approved';

        // Allow access if:
        // 1. Asset is approved (public access)
        // 2. User is logged in as the recipient (private access)
        if (isApprovedAsset || isLoggedInAsRecipient) {
          setAsset(assetWithTransfer);
          setCanView(true);

          // Fetch issuer information
          await fetchIssuerInfo(assetWithTransfer.issuer_odude_name);
        } else {
          // Asset exists but is not approved and user is not the recipient
          setAsset(assetWithTransfer);
          setCanView(false);
        }
      } else {
        // If no transfer found for this odude name, check if user is logged in and can view via email
        if (session?.user?.email) {
          const { data: userTransferData, error: userTransferError } = await fetchData('asset_transfers', {
            select: `
              *,
              assets (
                id,
                title,
                description,
                asset_type,
                image_url,
                template_id,
                issuer_odude_name,
                issuer_email,
                expiry_date,
                metadata,
                created_at
              )
            `,
            filter: [
              { column: 'asset_id', value: assetId },
              { column: 'to_email', value: session.user.email }
            ],
            single: true
          });

          if (!userTransferError && userTransferData && userTransferData.assets) {
            const assetWithTransfer: AssetWithTransfer = {
              ...userTransferData.assets,
              transfer: {
                id: userTransferData.id,
                status: userTransferData.status,
                transferred_at: userTransferData.transferred_at,
                to_odude_name: userTransferData.to_odude_name,
                to_email: userTransferData.to_email
              }
            };

            // User is the recipient, allow access regardless of status
            setAsset(assetWithTransfer);
            setCanView(true);

            // Fetch issuer information
            await fetchIssuerInfo(assetWithTransfer.issuer_odude_name);
          } else {
            notFound();
          }
        } else {
          notFound();
        }
      }
    } catch (error) {
      console.error('Error fetching asset:', error);
      notFound();
    } finally {
      setLoading(false);
    }
  };

  const handleBackClick = () => {
    window.history.back();
  };

  const handleProfileClick = () => {
    window.location.href = `/profile/${odudeName}`;
  };

  const handleIssuerClick = () => {
    if (asset?.issuer_odude_name) {
      window.location.href = `/profile/${asset.issuer_odude_name}`;
    }
  };

  if (loading) {
    return (
      <FullLayout>
        <LoadingOverlay visible />
      </FullLayout>
    );
  }

  // Show locked asset message if asset exists but cannot be viewed
  if (asset && !canView) {
    return (
      <FullLayout>
        <Stack gap="xl" align="center" style={{ minHeight: '60vh', justifyContent: 'center' }}>
          <ThemeIcon size={80} radius="xl" color="orange" variant="light">
            <IconAlertCircle size={40} />
          </ThemeIcon>

          <Stack gap="md" align="center" style={{ maxWidth: 500, textAlign: 'center' }}>
            <Title order={2} c="orange">Asset Locked</Title>
            <Text size="lg" c="dimmed">
              This asset is not publicly available. Only approved assets can be viewed by others.
            </Text>
            <Text size="sm" c="dimmed">
              If you are the recipient of this asset, please log in to view it.
            </Text>

            <Group>
              <Button
                variant="light"
                leftSection={<IconArrowLeft size={16} />}
                onClick={handleBackClick}
              >
                Go Back
              </Button>
              <Button
                onClick={() => window.location.href = '/auth/signin'}
                variant="filled"
              >
                Sign In
              </Button>
            </Group>
          </Stack>
        </Stack>
      </FullLayout>
    );
  }

  if (!asset || !canView) {
    notFound();
  }

  const isExpired = asset.expiry_date && new Date(asset.expiry_date) < new Date();

  return (
    <FullLayout>
      <Stack gap="xl">
        {/* Header with Back Button and Profile Link */}
        <Group justify="space-between" align="center">
          <Button
            variant="light"
            leftSection={<IconArrowLeft size={16} />}
            onClick={handleBackClick}
          >
            Back
          </Button>
          
          <Group>
            <Avatar
              src={getImage(userInfo?.images, 1)}
              size={60}
              radius={60}
              className={classes.avatar}
              onClick={handleProfileClick}
              style={{ cursor: 'pointer' }}
            />
            <div>
              <Text fw={500}>{odudeName}</Text>
              <Text c="dimmed" size="sm">Asset Showcase</Text>
            </div>
          </Group>
        </Group>

        {/* Asset Display */}
        <Card withBorder p="xl" radius="md">
          <Stack gap="lg" align="center">
            {/* Asset Image/Template */}
            <Box style={{ display: 'flex', justifyContent: 'center', width: '100%' }}>
              <AssetDisplay
                imageUrl={asset.image_url}
                templateId={asset.template_id}
                title={asset.title}
                width={500}
                height={400}
                fit="contain"
                assetData={{
                  title: asset.title,
                  description: asset.description || '',
                  issued_date: new Date(asset.created_at).toLocaleDateString(),
                  expire_date: asset.expiry_date ? new Date(asset.expiry_date).toLocaleDateString() : undefined,
                  receiver_full_name: userInfo?.profile || odudeName,
                  issuer_profile_name: asset.issuer_odude_name,
                  issuer_full_name: issuerInfo?.profile || asset.issuer_odude_name,
                  issuer_profile_avatar: getImage(issuerInfo?.images, 1),
                  receiver_profile_avatar: getImage(userInfo?.images, 1),
                  asset_image: asset.image_url
                }}
              />
            </Box>

            {/* Asset Details */}
            <Stack gap="md" align="center" style={{ width: '100%', maxWidth: 600 }}>
              <Title order={1} ta="center">{asset.title}</Title>

              {asset.description && (
                <Text ta="center" c="dimmed" size="lg">
                  {asset.description}
                </Text>
              )}

              <Group justify="center" gap="md">
                <Badge color={getAssetTypeColor(asset.asset_type)} variant="light" size="lg">
                  {asset.asset_type}
                </Badge>

                {asset.transfer && (
                  <Badge
                    color={
                      asset.transfer.status === 'approved' ? 'green' :
                      asset.transfer.status === 'pending' ? 'orange' :
                      asset.transfer.status === 'declined' ? 'red' : 'gray'
                    }
                    variant="light"
                    size="lg"
                  >
                    {asset.transfer.status.charAt(0).toUpperCase() + asset.transfer.status.slice(1)}
                  </Badge>
                )}

                {isExpired && (
                  <Badge color="red" variant="light" size="lg">
                    Expired
                  </Badge>
                )}
              </Group>

              {/* Asset Information */}
              <Stack gap="sm" style={{ width: '100%' }}>
                <Group justify="space-between">
                  <Group gap="xs">
                    <IconUser size={16} />
                    <Text size="sm" fw={500}>Issued by:</Text>
                  </Group>
                  <Anchor onClick={handleIssuerClick} style={{ cursor: 'pointer' }}>
                    <Group gap="xs">
                      <Avatar src={getImage(issuerInfo?.images, 1)} size={20} />
                      <Text size="sm">{asset.issuer_odude_name}</Text>
                      <IconExternalLink size={12} />
                    </Group>
                  </Anchor>
                </Group>

                <Group justify="space-between">
                  <Group gap="xs">
                    <IconCalendar size={16} />
                    <Text size="sm" fw={500}>Issued on:</Text>
                  </Group>
                  <Text size="sm">{new Date(asset.created_at).toLocaleDateString()}</Text>
                </Group>

                {asset.expiry_date && (
                  <Group justify="space-between">
                    <Group gap="xs">
                      <IconCalendar size={16} />
                      <Text size="sm" fw={500}>Expires on:</Text>
                    </Group>
                    <Text size="sm" c={isExpired ? 'red' : undefined}>
                      {new Date(asset.expiry_date).toLocaleDateString()}
                    </Text>
                  </Group>
                )}

                {asset.transfer && (
                  <Group justify="space-between">
                    <Group gap="xs">
                      <IconTrophy size={16} />
                      <Text size="sm" fw={500}>Received on:</Text>
                    </Group>
                    <Text size="sm">{new Date(asset.transfer.transferred_at).toLocaleDateString()}</Text>
                  </Group>
                )}
              </Stack>

              {isExpired && (
                <Alert icon={<IconAlertCircle size={16} />} color="red" variant="light">
                  This asset has expired and may no longer be valid.
                </Alert>
              )}
            </Stack>
          </Stack>
        </Card>
      </Stack>
    </FullLayout>
  );
}
