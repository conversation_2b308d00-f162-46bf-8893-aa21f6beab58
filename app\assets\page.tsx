'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import {
  Title,
  Text,
  Stack,
  Paper,
  Group,
  Badge,
  Button,
  ActionIcon,
  Image,
  Alert,
  LoadingOverlay,
  Tabs,
  Card,
  Grid,
  Modal,
  Textarea,
  ThemeIcon,
  Box,
  Anchor,
  Divider
} from '@mantine/core';
import {
  IconInbox,
  IconCheck,
  IconX,
  IconEyeOff,
  IconAlertCircle,
  IconPhoto,
  IconCalendar,
  IconUser,
  IconMessage,
  IconExternalLink
} from '@tabler/icons-react';
import { notifications } from '@mantine/notifications';
import { modals } from '@mantine/modals';
import { fetchData, updateData } from 'src/lib/supabase';
import { useAuthRedirect } from 'src/hooks/useAuthRedirect';
import FullLayout from 'src/components/layouts/FullLayout';
import {
  AssetTransfer,
  formatTransferForDisplay,
  canRespondToTransfer,
  getTransferStatusColor,
  isAssetExpired
} from 'src/lib/assets-utils';
import AssetDisplay from 'src/components/Assets/AssetDisplay';

interface AssetTransferWithAsset extends AssetTransfer {
  assets: {
    id: string;
    title: string;
    description: string | null;
    asset_type: 'Badge' | 'Certificate' | 'Ticket' | 'Coupon';
    image_url: string;
    issuer_odude_name: string;
    expiry_date: string | null;
    metadata: any;
    template_id: string | null;
  };
}

interface ResponseModalProps {
  transfer: AssetTransferWithAsset;
  opened: boolean;
  onClose: () => void;
  onRespond: () => void;
  action: 'approve' | 'decline';
}

function ResponseModal({ transfer, opened, onClose, onRespond, action }: ResponseModalProps) {
  const [responseNote, setResponseNote] = useState('');
  const [loading, setLoading] = useState(false);

  const handleSubmit = async () => {
    try {
      setLoading(true);

      const updatePayload = {
        status: action === 'approve' ? 'approved' : 'declined',
        responded_at: new Date().toISOString(),
        response_note: responseNote.trim() || null
      };

      const { error } = await updateData('asset_transfers', updatePayload, {
        column: 'id',
        value: transfer.id
      });

      if (error) throw error;

      notifications.show({
        title: 'Success',
        message: `Asset ${action === 'approve' ? 'approved' : 'declined'} successfully`,
        color: action === 'approve' ? 'green' : 'orange',
      });

      setResponseNote('');
      onRespond();
      onClose();

    } catch (error) {
      console.error(`Error ${action}ing asset:`, error);
      notifications.show({
        title: 'Error',
        message: `Failed to ${action} asset. Please try again.`,
        color: 'red',
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      opened={opened}
      onClose={onClose}
      title={`${action === 'approve' ? 'Approve' : 'Decline'} Asset`}
      centered
    >
      <Stack gap="md">
        <Card withBorder p="md">
          <Group>
            <Image
              src={transfer.assets.image_url}
              alt={transfer.assets.title}
              width={60}
              height={60}
              fit="cover"
              radius="md"
            />
            <div>
              <Text fw={500}>{transfer.assets.title}</Text>
              <Text size="sm" c="dimmed">From: {transfer.from_odude_name}</Text>
              <Badge color={getTransferStatusColor(transfer.status)} variant="light" size="sm">
                {transfer.assets.asset_type}
              </Badge>
            </div>
          </Group>
        </Card>

        <Textarea
          label="Response Note (Optional)"
          placeholder={`Add a note about why you ${action === 'approve' ? 'approved' : 'declined'} this asset...`}
          value={responseNote}
          onChange={(e) => setResponseNote(e.target.value)}
          minRows={3}
        />

        <Group justify="flex-end">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            loading={loading}
            color={action === 'approve' ? 'green' : 'orange'}
            leftSection={action === 'approve' ? <IconCheck size={16} /> : <IconX size={16} />}
          >
            {action === 'approve' ? 'Approve' : 'Decline'}
          </Button>
        </Group>
      </Stack>
    </Modal>
  );
}

export default function AssetsPage() {
  const { session, status, isLoading } = useAuthRedirect();
  const [transfers, setTransfers] = useState<AssetTransferWithAsset[]>([]);
  const [loading, setLoading] = useState(true);
  const [responseModal, setResponseModal] = useState<{
    opened: boolean;
    transfer: AssetTransferWithAsset | null;
    action: 'approve' | 'decline';
  }>({
    opened: false,
    transfer: null,
    action: 'approve'
  });

  useEffect(() => {
    if (session?.user?.email) {
      fetchTransfers();
    }
  }, [session]);

  const fetchTransfers = async () => {
    if (!session?.user?.email) return;

    try {
      setLoading(true);

      // Fetch transfers with asset details
      const { data, error } = await fetchData('asset_transfers', {
        select: `
          *,
          assets (
            id,
            title,
            description,
            asset_type,
            image_url,
            issuer_odude_name,
            expiry_date,
            metadata,
            template_id
          )
        `,
        filter: [
          { column: 'to_email', value: session.user.email }
        ]
      });

      if (!error && data) {
        const transfersArray = Array.isArray(data) ? data : [data];
        setTransfers(transfersArray);
      }
    } catch (error) {
      console.error('Error fetching transfers:', error);
      notifications.show({
        title: 'Error',
        message: 'Failed to load asset transfers',
        color: 'red',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleApprove = (transfer: AssetTransferWithAsset) => {
    setResponseModal({
      opened: true,
      transfer,
      action: 'approve'
    });
  };

  const handleDecline = (transfer: AssetTransferWithAsset) => {
    setResponseModal({
      opened: true,
      transfer,
      action: 'decline'
    });
  };

  const handleHide = async (transfer: AssetTransferWithAsset) => {
    modals.openConfirmModal({
      title: 'Hide Asset',
      children: (
        <Text size="sm">
          Are you sure you want to hide "{transfer.assets.title}"? 
          It will remain in your inbox but won't be visible on your public profile.
        </Text>
      ),
      labels: { confirm: 'Hide', cancel: 'Cancel' },
      confirmProps: { color: 'gray' },
      onConfirm: async () => {
        try {
          const { error } = await updateData(
            'asset_transfers',
            { status: 'hidden' },
            { column: 'id', value: transfer.id }
          );

          if (error) throw error;

          notifications.show({
            title: 'Success',
            message: 'Asset hidden successfully',
            color: 'green',
          });

          fetchTransfers();
        } catch (error) {
          console.error('Error hiding asset:', error);
          notifications.show({
            title: 'Error',
            message: 'Failed to hide asset',
            color: 'red',
          });
        }
      },
    });
  };

  // Show loading while authentication is being checked
  if (isLoading) {
    return (
      <FullLayout>
        <Paper withBorder p="md" style={{ position: 'relative', minHeight: 200 }}>
          <LoadingOverlay visible />
        </Paper>
      </FullLayout>
    );
  }

  // If not authenticated, the useAuthRedirect hook will handle the redirect
  if (!session) {
    return null;
  }

  if (loading) {
    return (
      <FullLayout>
        <Paper withBorder p="md" style={{ position: 'relative', minHeight: 200 }}>
          <LoadingOverlay visible />
        </Paper>
      </FullLayout>
    );
  }

  // Group transfers by status
  const pendingTransfers = transfers.filter(t => t.status === 'pending');
  const approvedTransfers = transfers.filter(t => t.status === 'approved');
  const declinedTransfers = transfers.filter(t => t.status === 'declined');
  const hiddenTransfers = transfers.filter(t => t.status === 'hidden');

  const renderTransferCard = (transfer: AssetTransferWithAsset) => {
    // Extract just the transfer data for formatting
    const transferData: AssetTransfer = {
      id: transfer.id,
      asset_id: transfer.asset_id,
      from_odude_name: transfer.from_odude_name,
      from_email: transfer.from_email,
      to_odude_name: transfer.to_odude_name,
      to_email: transfer.to_email,
      status: transfer.status,
      transferred_at: transfer.transferred_at,
      responded_at: transfer.responded_at,
      response_note: transfer.response_note
    };
    const formattedTransfer = formatTransferForDisplay(transferData);
    const expired = transfer.assets?.expiry_date ? new Date(transfer.assets.expiry_date) < new Date() : false;
    const canRespond = canRespondToTransfer(transfer, session?.user?.email || '');

    const handleAssetClick = () => {
      // Navigate to individual asset page - use the to_odude_name from the transfer
      window.location.href = `/profile/assets/${transfer.to_odude_name}/${transfer.assets.id}`;
    };

    return (
      <Card
        key={transfer.id}
        withBorder
        p="md"
        style={{
          opacity: expired ? 0.6 : 1,
          height: '100%',
          cursor: 'pointer'
        }}
        onClick={handleAssetClick}
      >
        <Card.Section>
          <AssetDisplay
            imageUrl={transfer.assets.image_url}
            templateId={transfer.assets.template_id}
            title={transfer.assets.title}
            width={300}
            height={200}
            fit="cover"
            assetData={{
              title: transfer.assets.title,
              description: transfer.assets.description || '',
              issued_date: new Date(transfer.created_at).toLocaleDateString(),
              expire_date: transfer.assets.expiry_date ? new Date(transfer.assets.expiry_date).toLocaleDateString() : undefined,
              receiver_full_name: session?.user?.name || session?.user?.email || 'Recipient',
              issuer_profile_name: transfer.assets.issuer_odude_name,
              asset_image: transfer.assets.image_url,
              // Keep backward compatibility
              full_name: session?.user?.name || session?.user?.email || 'Recipient',
              profile_name: transfer.assets.issuer_odude_name
            }}
          />
        </Card.Section>

        <Stack gap="sm" mt="md">
          <Group justify="space-between" align="flex-start">
            <div style={{ flex: 1 }}>
              <Text fw={500} lineClamp={2}>{transfer.assets.title}</Text>
              {transfer.assets.description && (
                <Text size="sm" c="dimmed" lineClamp={2}>
                  {transfer.assets.description}
                </Text>
              )}
            </div>
            <Badge color={getTransferStatusColor(transfer.status)} variant="light" size="sm">
              {transfer.assets.asset_type}
            </Badge>
          </Group>

          <Badge color={formattedTransfer.statusColor} variant="outline" size="sm">
            {formattedTransfer.statusLabel}
          </Badge>

          <Divider />

          <div>
            <Group gap="xs" mb="xs">
              <IconUser size={14} />
              <Text size="sm">From: </Text>
              <Anchor
                size="sm"
                href={`/profile/${transfer.from_odude_name}`}
                target="_blank"
                style={{ textDecoration: 'none' }}
              >
                {transfer.from_odude_name}
              </Anchor>
            </Group>
            <Group gap="xs" mb="xs">
              <IconUser size={14} />
              <Text size="sm">Assigned to: {transfer.to_odude_name}</Text>
            </Group>
            <Group gap="xs" mb="xs">
              <IconCalendar size={14} />
              <Text size="sm">Received: {formattedTransfer.formattedTransferredAt}</Text>
            </Group>
            {transfer.assets.expiry_date && (
              <Group gap="xs" mb="xs">
                <IconCalendar size={14} />
                <Text size="sm" c={expired ? 'red' : 'dimmed'}>
                  Expires: {new Date(transfer.assets.expiry_date).toLocaleDateString()}
                </Text>
              </Group>
            )}
            {expired && <Badge color="red" size="xs">Expired</Badge>}
          </div>

          {canRespond && !expired && (
            <Group gap="xs">
              <Button
                size="xs"
                color="green"
                leftSection={<IconCheck size={14} />}
                onClick={(e) => {
                  e.stopPropagation();
                  handleApprove(transfer);
                }}
              >
                Approve
              </Button>
              <Button
                size="xs"
                color="orange"
                variant="outline"
                leftSection={<IconX size={14} />}
                onClick={(e) => {
                  e.stopPropagation();
                  handleDecline(transfer);
                }}
              >
                Decline
              </Button>
            </Group>
          )}

          {transfer.status === 'approved' && (
            <Button
              size="xs"
              color="gray"
              variant="outline"
              leftSection={<IconEyeOff size={14} />}
              onClick={(e) => {
                e.stopPropagation();
                handleHide(transfer);
              }}
            >
              Hide
            </Button>
          )}
        </Stack>

        {transfer.response_note && (
          <Box mt="md" p="sm" style={{ backgroundColor: '#f8f9fa', borderRadius: '4px' }}>
            <Group gap="xs" mb="xs">
              <IconMessage size={14} />
              <Text size="sm" fw={500}>Response Note:</Text>
            </Group>
            <Text size="sm">{transfer.response_note}</Text>
          </Box>
        )}
      </Card>
    );
  };

  return (
    <FullLayout>
      <Stack gap="xl">
        {/* Header */}
        <Group>
          <ThemeIcon size={40} radius="md" color="blue">
            <IconInbox size={24} />
          </ThemeIcon>
          <div>
            <Title order={1}>Asset Inbox</Title>
            <Text c="dimmed">Manage assets sent to you by others</Text>
          </div>
        </Group>

        {transfers.length === 0 ? (
          <Paper withBorder p="xl" style={{ textAlign: 'center' }}>
            <IconPhoto size={48} color="gray" style={{ margin: '0 auto 16px' }} />
            <Text size="lg" fw={500} mb="xs">No Assets Received</Text>
            <Text c="dimmed">
              You haven't received any assets yet. When someone sends you an asset, it will appear here.
            </Text>
          </Paper>
        ) : (
          <Tabs defaultValue="pending" variant="outline">
            <Tabs.List>
              <Tabs.Tab value="pending" leftSection={<IconInbox size={16} />}>
                Pending ({pendingTransfers.length})
              </Tabs.Tab>
              <Tabs.Tab value="approved" leftSection={<IconCheck size={16} />}>
                Approved ({approvedTransfers.length})
              </Tabs.Tab>
              <Tabs.Tab value="declined" leftSection={<IconX size={16} />}>
                Declined ({declinedTransfers.length})
              </Tabs.Tab>
              <Tabs.Tab value="hidden" leftSection={<IconEyeOff size={16} />}>
                Hidden ({hiddenTransfers.length})
              </Tabs.Tab>
            </Tabs.List>

            <Tabs.Panel value="pending" pt="md">
              {pendingTransfers.length === 0 ? (
                <Alert icon={<IconAlertCircle size={16} />} color="blue">
                  No pending assets to review.
                </Alert>
              ) : (
                <Grid>
                  {pendingTransfers.map((transfer) => (
                    <Grid.Col span={{ base: 12, sm: 6, md: 4, lg: 4, xl: 3 }} key={transfer.id}>
                      {renderTransferCard(transfer)}
                    </Grid.Col>
                  ))}
                </Grid>
              )}
            </Tabs.Panel>

            <Tabs.Panel value="approved" pt="md">
              {approvedTransfers.length === 0 ? (
                <Alert icon={<IconAlertCircle size={16} />} color="green">
                  No approved assets yet.
                </Alert>
              ) : (
                <Grid>
                  {approvedTransfers.map((transfer) => (
                    <Grid.Col span={{ base: 12, sm: 6, md: 4, lg: 4, xl: 3 }} key={transfer.id}>
                      {renderTransferCard(transfer)}
                    </Grid.Col>
                  ))}
                </Grid>
              )}
            </Tabs.Panel>

            <Tabs.Panel value="declined" pt="md">
              {declinedTransfers.length === 0 ? (
                <Alert icon={<IconAlertCircle size={16} />} color="orange">
                  No declined assets.
                </Alert>
              ) : (
                <Grid>
                  {declinedTransfers.map((transfer) => (
                    <Grid.Col span={{ base: 12, sm: 6, md: 4, lg: 4, xl: 3 }} key={transfer.id}>
                      {renderTransferCard(transfer)}
                    </Grid.Col>
                  ))}
                </Grid>
              )}
            </Tabs.Panel>

            <Tabs.Panel value="hidden" pt="md">
              {hiddenTransfers.length === 0 ? (
                <Alert icon={<IconAlertCircle size={16} />} color="gray">
                  No hidden assets.
                </Alert>
              ) : (
                <Grid>
                  {hiddenTransfers.map((transfer) => (
                    <Grid.Col span={{ base: 12, sm: 6, md: 4, lg: 4, xl: 3 }} key={transfer.id}>
                      {renderTransferCard(transfer)}
                    </Grid.Col>
                  ))}
                </Grid>
              )}
            </Tabs.Panel>
          </Tabs>
        )}

        {/* Response Modal */}
        {responseModal.transfer && (
          <ResponseModal
            transfer={responseModal.transfer}
            opened={responseModal.opened}
            action={responseModal.action}
            onClose={() => setResponseModal({ opened: false, transfer: null, action: 'approve' })}
            onRespond={fetchTransfers}
          />
        )}
      </Stack>
    </FullLayout>
  );
}
